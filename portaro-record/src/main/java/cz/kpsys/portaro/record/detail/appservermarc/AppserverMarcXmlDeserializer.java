package cz.kpsys.portaro.record.detail.appservermarc;

import cz.kpsys.portaro.commons.convert.StringToIntegerConverter;
import cz.kpsys.portaro.commons.convert.StringToUuidConverter;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.record.detail.IndicatorType;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.jdom2.Element;
import org.jdom2.Namespace;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
public class AppserverMarcXmlDeserializer {

    public static final String RECORD = "record";
    public static final String RECORD_ID = "record_id";
    public static final String FOND = "fond";
    public static final String LEADER = "leader";
    public static final String CONTROLFIELD = "controlfield";
    public static final String DATAFIELD = "datafield";
    public static final String TOC = "toc";
    public static final String SUBFIELD = "subfield";
    public static final String NUMBER = "tag";
    public static final String IND1 = "ind1";
    public static final String IND2 = "ind2";
    public static final String ID = "id";
    public static final String SUBFIELD_RECORD_FOND_ID = "fond_id";
    public static final String SUBFIELD_RECORD_ID = "record_id";
    public static final String CODE = "code";

    public RecordAppserverMarcResponse deserialize(Element recordElem, Namespace ns) {
        UUID recordId = StringToUuidConverter.INSTANCE.convert(recordElem.getAttributeValue(RECORD_ID));

        Integer fondId = mapFond(recordElem.getAttributeValue(FOND));

        LeaderAppserverMarcResponse leader = mapLeader(recordElem.getChild(LEADER, ns));

        TocAppserverMarcResponse toc = mapToc(recordElem.getChild(TOC, ns));

        List<ControlfieldAppserverMarcResponse> controlfields = ListUtil.convert(recordElem.getChildren(CONTROLFIELD, ns), this::mapControlfield);

        List<DatafieldAppserverMarcResponse> datafields = ListUtil.convert(recordElem.getChildren(DATAFIELD, ns), this::mapDatafields);

        return new RecordAppserverMarcResponse(recordId, fondId, leader, controlfields, datafields, toc);
    }


    @Nullable
    private Integer mapFond(@Nullable String fondAttributeValue) {
        if (fondAttributeValue == null) {
            return null;
        }
        return new StringToIntegerConverter().convert(fondAttributeValue);
    }


    private LeaderAppserverMarcResponse mapLeader(@NonNull Element leaderElement) {
        String textTrim = leaderElement.getTextTrim();
        if (textTrim != null) {
            return new LeaderAppserverMarcResponse(textTrim);
        }
        throw new DataAccessException("Record marc21 data contain empty leader element", "marc21 xml");
    }


    @Nullable
    private TocAppserverMarcResponse mapToc(@Nullable Element tocElement) {
        if (tocElement == null) {
            return null;
        }
        String pdf = tocElement.getAttributeValue("pdf");
        if (StringUtil.isNullOrBlank(pdf)) {
            log.warn("Record marc xml contains toc element, but pdf attribute is null or blank: {}", pdf);
        }

        String text = tocElement.getText();
        if (StringUtil.isNullOrBlank(text)) {
            throw new DataAccessException("Record marc21 data contain toc element, but is empty", "marc21 xml");
        }
        text = text
                .replace(" -- ", "\n")
                .replace(" --", "\n")
                .replace("-- ", "\n")
                .replace("--", "\n");
        return new TocAppserverMarcResponse(pdf, text);
    }


    private ControlfieldAppserverMarcResponse mapControlfield(@NonNull Element controlfieldElement) {
        String number = getTopfieldCodeFromTag(controlfieldElement);
        String raw = controlfieldElement.getText();
        return new ControlfieldAppserverMarcResponse(number, raw);
    }


    private DatafieldAppserverMarcResponse mapDatafields(@NonNull Element datafieldElem) {
        String number = getTopfieldCodeFromTag(datafieldElem);
        String ind1 = IndicatorType.fromMarcXmlIndicatorValue(String.valueOf(datafieldElem.getAttributeValue(IND1).charAt(0)));
        String ind2 = IndicatorType.fromMarcXmlIndicatorValue(String.valueOf(datafieldElem.getAttributeValue(IND2).charAt(0)));
        List<SubfieldAppserverMarcResponse> subfields = ListUtil.convert(datafieldElem.getChildren(SUBFIELD, datafieldElem.getNamespace()), this::mapMarcSubfield);
        return new DatafieldAppserverMarcResponse(number, ind1, ind2, subfields);
    }


    private SubfieldAppserverMarcResponse mapMarcSubfield(@NonNull Element subfieldElem) {
        String code = subfieldElem.getAttributeValue(CODE);
        String raw = subfieldElem.getTextTrim();
        UUID recordId = null;
        Integer fondId = null;

        if (subfieldElem.getAttribute(SUBFIELD_RECORD_ID) != null) {
            //element obsahuje id -> jedna se o odkaz na jiny record
            recordId = UUID.fromString(subfieldElem.getAttributeValue(SUBFIELD_RECORD_ID));
            fondId = Integer.parseInt(Objects.requireNonNull(subfieldElem.getAttributeValue(SUBFIELD_RECORD_FOND_ID), "Appserver did not send " + SUBFIELD_RECORD_FOND_ID + " attribute in subfield element"));
        }

        return new SubfieldAppserverMarcResponse(code, raw, recordId, fondId);
    }


    private static String getTopfieldCodeFromTag(@NonNull Element fieldElement) {
        String tagValue = fieldElement.getAttributeValue(NUMBER);
        Integer number = new StringToIntegerConverter().convert(tagValue);
        return String.valueOf(number);
    }
}
