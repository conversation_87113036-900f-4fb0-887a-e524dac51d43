package cz.kpsys.portaro.proveniencesmap;

import cz.kpsys.portaro.maps.Coordinates;
import cz.kpsys.portaro.maps.Geocoder;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.TestingFieldTypeLoader;
import cz.kpsys.portaro.record.detail.appservermarc.MarcXmlToDetailConverterImpl;
import cz.kpsys.portaro.record.detail.value.StringFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Tag("ci")
@Tag("unit")
public class AuthorityToHistoricalPlacesConverterTest {

    Geocoder mockGeocoder = (country, city) -> new Coordinates(0, 0);
    Record authority;
    private final TestingFieldTypeLoader fieldTypeLoader = new TestingFieldTypeLoader()
            .withComplexDatafieldType("a386", List.of("a"));

    @BeforeEach
    public void prepare() {
        Fond fond = Fond.testingPerson();
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UUID.fromString("53581539-d6ff-4b1a-8e64-88f8e45a154f"), fond);

        MarcXmlToDetailConverterImpl constructor = MarcXmlToDetailConverterImpl.testing(fond, fieldTypeLoader);
        FieldContainer detail = constructor.convertSingle("""
                <collection xmlns="http://www.loc.gov/MARC21/slim">
                    <record status="3" id="123" record_id="53581539-d6ff-4b1a-8e64-88f8e45a154f" fond="31">
                      <leader>-----nam-a22-----1a-4500</leader>
                      <datafield tag="100" ind1="#" ind2="#">
                        <subfield code="a">Zámecká knihovna</subfield>
                        <subfield code="b">Český Krumlov</subfield>
                      </datafield>
                      <datafield tag="245" ind1="#" ind2="#">
                        <subfield code="a">Zámecká knihovna Český Krumlov</subfield>
                      </datafield>
                      <datafield tag="386" ind1="#" ind2="#">
                        <subfield code="a" fond_id="41" record_id="b40a36a3-d2f1-4e6d-9872-4d8b586c22d1">Šlechtické knihovny</subfield>
                      </datafield>
                      <datafield tag="386" ind1="#" ind2="#">
                        <subfield code="a" fond_id="41" record_id="32743e08-2172-4671-b8e5-9a99577b0319">Architektonické knihovny</subfield>
                      </datafield>
                      <datafield tag="386" ind1="#" ind2="#">
                        <subfield code="a" fond_id="41" record_id="40fb30c8-4cf7-4921-8c54-b61f614cd406">Biologické knihovny</subfield>
                      </datafield>
                      <datafield tag="370" ind1="#" ind2="#">
                        <subfield code="c">Česká republika</subfield>
                        <subfield code="f">Český Krumlov</subfield>
                        <subfield code="s">1665</subfield>
                        <subfield code="t">1719</subfield>
                      </datafield>
                      <datafield tag="370" ind1="#" ind2="#">
                        <subfield code="c">Česká republika</subfield>
                        <subfield code="f">Pardubice</subfield>
                        <subfield code="s">1710</subfield>
                        <subfield code="t">1810</subfield>
                      </datafield>
                    </record>
                </collection>""");
        authority = new Record(recordIdFondPair, 123, null, false, null, detail, ObjectProperties.empty());
    }

    @Test
    public void testConvert() {
        AuthorityToHistoricalPlacesConverter converter = new AuthorityToHistoricalPlacesConverter(new CoordinatesFromFieldFinder(mockGeocoder), new PlaceTypeConverter());

        List<HistoricalPlace> hps = Objects.requireNonNull(converter.convert(authority)).successfullyConvertedPlaces();

        assertNotNull(hps);
        assertEquals(2, hps.size());
        assertEquals(UUID.fromString("53581539-d6ff-4b1a-8e64-88f8e45a154f"), hps.getFirst().recordId());
        assertEquals(1665, hps.getFirst().fromYear());
        assertEquals(1719, hps.getFirst().toYear());
        assertEquals(ProvenienceType.SLECHTICKA_KNIHOVNA, hps.getFirst().type());
        assertEquals(List.of(ProfessionalType.ARCHITEKTONICKA_KNIHOVNA, ProfessionalType.BIOLOGICKA_KNIHOVNA), hps.getFirst().professionalType());
        assertEquals(List.of(ProvenienceType.SLECHTICKA_KNIHOVNA, ProfessionalType.ARCHITEKTONICKA_KNIHOVNA, ProfessionalType.BIOLOGICKA_KNIHOVNA), hps.getFirst().allTypes());
        assertEquals("Zámecká knihovna Český Krumlov", hps.getFirst().name());
        assertEquals(UUID.fromString("53581539-d6ff-4b1a-8e64-88f8e45a154f"), hps.get(1).recordId());
        assertEquals(1710, hps.get(1).fromYear());
        assertEquals(1810, hps.get(1).toYear());
        assertEquals(ProvenienceType.SLECHTICKA_KNIHOVNA, hps.get(1).type());
        assertEquals(List.of(ProfessionalType.ARCHITEKTONICKA_KNIHOVNA, ProfessionalType.BIOLOGICKA_KNIHOVNA), hps.get(1).professionalType());
        assertEquals(List.of(ProvenienceType.SLECHTICKA_KNIHOVNA, ProfessionalType.ARCHITEKTONICKA_KNIHOVNA, ProfessionalType.BIOLOGICKA_KNIHOVNA), hps.get(1).allTypes());
        assertEquals("Zámecká knihovna Český Krumlov", hps.get(1).name());
    }

    @Test
    public void shouldConvertWholeCentury() {
        Field<?> sfStartYear = authority.getDetail().getFirstField(By.code("a370"), By.code("s")).get();
        String val;
        val = "16. stol.";
        sfStartYear.setValue(StringFieldValue.of(val));
        Field<?> sfEndYear = authority.getDetail().getFirstField(By.code("a370"), By.code("t")).get();
        val = "ca.1621";
        sfEndYear.setValue(StringFieldValue.of(val));

        AuthorityToHistoricalPlacesConverter converter = new AuthorityToHistoricalPlacesConverter(new CoordinatesFromFieldFinder(mockGeocoder), new PlaceTypeConverter());
        List<HistoricalPlace> hps = Objects.requireNonNull(converter.convert(authority)).successfullyConvertedPlaces();

        assertNotNull(hps);
        assertEquals(1501, hps.getFirst().fromYear());
        assertEquals(1621, hps.getFirst().toYear());
    }

    @Test
    public void shouldSkipNotExistingSpacetimeField() {
        //odstranime podpole 370#0.f#0
        Field<?> f370 = authority.getDetail().getFirstField(By.code("a370")).orElseThrow();
        f370.remove(f370.getFirstField(By.code("f")).orElseThrow());

        AuthorityToHistoricalPlacesConverter converter = new AuthorityToHistoricalPlacesConverter(new CoordinatesFromFieldFinder(mockGeocoder), new PlaceTypeConverter());
        List<HistoricalPlace> hps = Objects.requireNonNull(converter.convert(authority)).successfullyConvertedPlaces();

        assertNotNull(hps);
        assertEquals(1, hps.size());
        assertEquals(UUID.fromString("53581539-d6ff-4b1a-8e64-88f8e45a154f"), hps.getFirst().recordId());
        assertEquals(1710, hps.getFirst().fromYear());
        assertEquals(1810, hps.getFirst().toYear());
    }

}
