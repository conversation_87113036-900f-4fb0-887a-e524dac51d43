<script lang="ts">
    import type {User} from 'typings/portaro.be.types';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {onMount} from 'svelte';
    import html2canvas from 'html2canvas';
    import JsBarcode from 'jsbarcode';
    import cardBgImg from './assets/reader-card-bg.webp';
    import googlePayIcon from './assets/googlepay.png';
    import appleWalletIcon from './assets/applewallet.png';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    let barcodeElement: SVGSVGElement;

    export let user: User;
    const barCode = user.readerAccounts[0].barCode;

    let cardElement: HTMLDivElement;

    onMount(() => {
        JsBarcode(barcodeElement, barCode, {
            format: 'CODE128',
            lineColor: '#FFF',
            background: '#********',
            margin: 0,
            height: 50,
            displayValue: false
        });
    });

    const handleCardDownload = async () => {
        const canvas = await html2canvas(cardElement, {
            backgroundColor: null
        });
        const image = canvas.toDataURL('image/png');

        const link = document.createElement('a');
        link.href = image;
        link.download = 'karticka-ctenare.png';
        link.click();
    };
</script>

<Flex direction="column" gap="ml">
    <div class="user-barcode-card" bind:this={cardElement}>
        <img alt="card-background" src="{cardBgImg}" class="card-background"/>

        <span class="card-title">Kartička čtenáře knihovny</span>
        <span class="name">{pipe(user, loc())}</span>

        <Spacer flex="1"/>

        <Flex direction="column" width="100%" gap="s">
            <svg class="barcode" bind:this={barcodeElement}></svg>
            <span class="barcode-value">{barCode}</span>
            <span class="code-label">CODE128</span>
        </Flex>

        <img src="/resources/img/brand/verbis-logo-orange.svg" height="20px" class="logo" alt="verbis.io"/>
    </div>

    <Flex alignItems="center" gap="m">
        <KpButton buttonStyle="primary" on:click={handleCardDownload}>
            <IconedContent icon="download">
                Stáhnout kartičku jako obrázek
            </IconedContent>
        </KpButton>

        <KpButton>
            <IconedContent>
                <svelte:fragment slot="icon">
                    <img class="wallet-icon" src="{appleWalletIcon}" alt="Apple Wallet"/>
                </svelte:fragment>

                Uložit do Apple Wallet
            </IconedContent>
        </KpButton>

        <KpButton>
            <IconedContent icon="print">
                <svelte:fragment slot="icon">
                    <img class="wallet-icon" src="{googlePayIcon}" alt="Google Pay"/>
                </svelte:fragment>

                Uložit do Google Pay
            </IconedContent>
        </KpButton>
    </Flex>
</Flex>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    @card-padding: 24px;

    .user-barcode-card {
        width: 450px;
        height: 275px;
        border-radius: 12px;
        overflow: hidden;
        isolation: isolate;
        padding: @card-padding;
        position: relative;
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
        color: white;
        box-shadow: rgba(0, 0, 0, 0.07) 0 1px 2px, rgba(0, 0, 0, 0.07) 0 2px 4px, rgba(0, 0, 0, 0.07) 0 4px 8px, rgba(0, 0, 0, 0.07) 0 8px 16px, rgba(0, 0, 0, 0.07) 0 16px 32px, rgba(0, 0, 0, 0.07) 0 32px 64px;

        .card-title {
            font-size: @font-size-small;
            opacity: 0.75;
        }

        .name {
            font-size: @font-size-xl;
            font-weight: 500;
        }

        .barcode-value {
            font-size: @font-size-small;
        }

        .code-label {
            margin-top: @spacing-xs;
            font-size: @font-size-xs;
            opacity: 0.75;
        }

        .card-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -1;
        }

        .logo {
            position: absolute;
            top: @card-padding;
            right: @card-padding;
        }
    }

    .wallet-icon {
        height: 14px;
    }
</style>