import type {AnyObject, EmptyObject} from 'typings/portaro.fe.types';
import type {StateManager} from 'shared/state-manager/state-manager';
import type {LoadListItemsFunction, LoadListSizeFunction} from 'shared/utils/reactive-pageable-list';
import {transferify} from 'shared/utils/data-service-utils';
import {BehaviorSubject, EMPTY, firstValueFrom, from, type Observable, switchMap} from 'rxjs';
import type {PageRequest} from './pagination';
import {Pagination} from './pagination';
import {catchError, distinctUntilChanged, map, tap} from 'rxjs/operators';
import {exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import {DefaultReactivePageableList} from 'shared/utils/default-reactive-pageable-list';
import {shareWithRefCountAndReplayLast} from 'shared/utils/observables-utils';
import {isEqual, omitBy, pick} from 'lodash-es';
import type {Form, Identified, Pageable, PagedParameteredResult, SearchParams, StaticSearchParams, ViewableSearch} from 'typings/portaro.be.types';
import {DerivedStateManager} from 'shared/state-manager/derived-state-manager';
import {isNonEmptyString} from 'shared/utils/string-utils';
import type {Logger} from 'core/logging/types';

export interface SearchManagerState<PARAMS extends SearchParams = SearchParams> {
    loadPageErrorMessage: string | null;
    pageLoadInProgress: Pageable | null;
    frontendParams: Partial<PARAMS>;
}

export class SearchManager<ITEM, PARAMS extends SearchParams = SearchParams> {

    private readonly asJsonStringUrlSerializedFields = ['facetRestriction', 'qt'];
    private readonly defaultAlwaysFrontendParamsPassingSearchFields: (keyof SearchParams)[] = ['sorting', 'pageSize', 'pageNumber', 'facetRestriction'];
    private readonly forceReloadDateParameterName: string = 'searchDate';
    private readonly searchResult$: Observable<PagedParameteredResult<ITEM, PARAMS>>;
    private readonly lastSearch$: Observable<ViewableSearch<PARAMS, ITEM>>;
    private readonly stateSource$: BehaviorSubject<SearchManagerState<PARAMS>>;
    private readonly state$: Observable<SearchManagerState<PARAMS>>;
    private readonly pagination: Pagination<ITEM>;
    private readonly staticParams: Partial<StaticSearchParams>;
    private frontendParams: Partial<PARAMS> = {} as Partial<PARAMS>;
    private search: ViewableSearch<PARAMS, ITEM> = null;
    private forms: Form[] = [];

    constructor(private logger: Logger,
                private stateManager: StateManager<PARAMS>,
                staticParams: Partial<StaticSearchParams>,
                private loadSearch: (params: Partial<PARAMS>) => Promise<ViewableSearch<PARAMS, ITEM>>,
                private loadFormsFunction?: (_: StaticSearchParams | AnyObject) => Promise<Form[]>) {

        this.staticParams = transferify(staticParams);
        this.stateSource$ = new BehaviorSubject(this.createCurrentState());
        this.state$ = this.stateSource$.pipe(distinctUntilChanged(isEqual), shareWithRefCountAndReplayLast());

        this.lastSearch$ = stateManager.getState$().pipe(
            map((state) => this.prepareParams(state)),
            switchMap((params) => this.fetchSearch(params)),
            map(([search, params]) => this.saveSearch(search, params)),
            shareWithRefCountAndReplayLast()
        );
        this.searchResult$ = this.lastSearch$.pipe(
            map((search) => this.getResult(search)),
            shareWithRefCountAndReplayLast()
        );
        this.pagination = new Pagination(this, this.logger);
    }

    public getSearchResult$(): Observable<PagedParameteredResult<ITEM, PARAMS>> {
        return this.searchResult$;
    }

    public getLastSearch$(): Observable<ViewableSearch<PARAMS, ITEM>> {
        return this.lastSearch$;
    }

    public getState$(): Observable<SearchManagerState<PARAMS>> {
        return this.state$;
    }

    public requestPage({ pageNumber, afterPosition, beforePosition }: PageRequest): void {
        let params = {pageNumber} as PARAMS;

        if (exists(afterPosition) && isNonEmptyString(afterPosition)) {
            params = {...params, afterPosition};
        }

        if (exists(beforePosition) && isNonEmptyString(beforePosition)) {
            params = {...params, beforePosition};
        }

        this.stateManager.requestChangeState(params);
    }

    public requestPageReload(pageNumber: number): void {
        this.frontendParams.pageNumber = pageNumber;
        this.refreshSearch();
    }

    public reset(): void {
        this.pagination.reset(this.frontendParams.pageSize);
    }

    public newSearch(): void {
        this.reset();
        this.frontendParams.pageNumber = 1;
        this.requestParametersChange(this.frontendParams);
    }

    public newSearchWithParams(newParams: PARAMS) {
        this.setFrontendParams(newParams);
        this.newSearch();
    }

    public newSearchWithPartialParams(partialNewParams: Partial<PARAMS>) {
        this.newSearchWithParams({...this.getFrontendParams(), ...partialNewParams});
    }

    /**
     * if url parameter wouldn't change, and we still want to reload search - e.g. after deleting some item from searched list, we want to force reload search. Currently, this is realized by adding special searchDate parameter, but there should be better solution.
     */
    public refreshSearch(): void {
        this.frontendParams[this.forceReloadDateParameterName] = new Date().toISOString();
        this.requestParametersChange(this.frontendParams);
    }

    public getStaticParams(): Readonly<Partial<StaticSearchParams>> {
        return this.staticParams;
    }

    public getFrontendParams(): Readonly<Partial<PARAMS>> {
        return this.frontendParams;
    }

    public setFrontendParams(params: Partial<PARAMS>): void {
        this.frontendParams = params;
        this.stateSource$.next(this.createCurrentState());
    }

    public getCompleteParams(): Readonly<Partial<PARAMS>> {
        return this.prepareParams(this.frontendParams);
    }

    public lastSearch(): ViewableSearch<PARAMS, ITEM> | null {
        return this.search;
    }

    public lastResult(): PagedParameteredResult<ITEM, PARAMS> | undefined {
        return this.search?.result;
    }

    public getForms(): Form[] {
        return this.forms;
    }

    public async loadForms(): Promise<void> {
        if (this.loadFormsFunction) {
            this.forms = await this.loadFormsFunction(this.staticParams);
        } else {
            this.forms = (await this.loadSearch(this.prepareParams(this.staticParams as Partial<PARAMS>))).forms;
        }
    }

    public createDerivedSearch(paramsModifier: (params: PARAMS) => PARAMS): SearchManager<ITEM, PARAMS> {
        return new SearchManager<ITEM, PARAMS>(
            this.logger,
            new DerivedStateManager(this.stateManager, paramsModifier),
            this.staticParams,
            this.loadSearch,
            this.loadFormsFunction);
    }

    public getPagination(): Pagination<ITEM> {
        return this.pagination;
    }

    private requestParametersChange(newParams: Partial<PARAMS>): void {
        const transferable = transferify(newParams);
        this.asJsonStringUrlSerializedFields
            .filter((fieldName) => Object.hasOwn(transferable, fieldName))
            .forEach((fieldName) => transferable[fieldName] = JSON.stringify(transferable[fieldName]));

        this.stateManager.requestChangeState(transferable);
    }

    private prepareParams(newParams: Partial<PARAMS>): Partial<PARAMS> {
        this.asJsonStringUrlSerializedFields
            .filter((fieldName) => Object.hasOwn(newParams, fieldName))
            // We check if the param is type string, then serialize it - in a case it was taken from URL - otherwise it is already deserialized, so we use it as is
            .forEach((fieldName) => newParams[fieldName] = this.tryParse(newParams[fieldName]));
        const nonNullParams: Partial<PARAMS> = omitBy(newParams, (value) => value === null) as Partial<PARAMS>;
        return {...this.staticParams, ...nonNullParams};
    }

    private tryParse(parsedObject: any): any {
        try {
            return JSON.parse(parsedObject);
        } catch {
            return parsedObject;
        }
    }

    private fetchSearch(params: Partial<PARAMS>): Observable<readonly [ViewableSearch<PARAMS, ITEM>, Partial<PARAMS>]> {
        this.onPageLoadStart({pageSize: params.pageSize, pageNumber: params.pageNumber});
        const promise = this.loadSearch(params).then((search) => [search, params] as const);
        return from(promise).pipe(
            tap(() => this.onPageLoadSuccess()),
            catchError((exceptionResponse) => {
                    this.onPageLoadError(exceptionResponse)
                    return EMPTY;
                },
            ));
    }

    private saveSearch(search: ViewableSearch<PARAMS, ITEM>, currentParams: Partial<PARAMS>): ViewableSearch<PARAMS, ITEM> {
        this.search = search;
        this.forms = this.search?.forms ?? this.forms;
        const result = this.search.result;

        this.setFrontendParams(this.buildNewFrontendParams(currentParams, result));
        return search;
    }

    private getResult(search: ViewableSearch<PARAMS, ITEM>): PagedParameteredResult<ITEM, PARAMS> {
        return search.result
    }

    private buildNewFrontendParams(currentLoadPageParams: Partial<PARAMS>, result: PagedParameteredResult<ITEM, PARAMS>): Partial<PARAMS> {
        const allFormsSearchFieldNames = (this.forms || [])
            .flatMap((form) => form.fields || [])
            .map((field) => field.fieldName);
        const allowedSearchFieldNames = [...this.defaultAlwaysFrontendParamsPassingSearchFields, ...allFormsSearchFieldNames];
        const allowedResultParamsFieldsParams = pick(result.params, allowedSearchFieldNames); // returns object with only fields filtered by allowedSearchFieldNames

        return {
            ...currentLoadPageParams,
            ...this.frontendParams,
            ...allowedResultParamsFieldsParams,
            pageSize: result.pageSize,
            pageNumber: result.pageNumber
        };
    }

    private createCurrentState(): SearchManagerState<PARAMS> {
        return {
            frontendParams: {...this.getFrontendParams()},
            loadPageErrorMessage: null,
            pageLoadInProgress: null
        }
    }

    private onPageLoadStart(pageable: Pageable): void {
        this.stateSource$.next({...this.createCurrentState(), pageLoadInProgress: pageable});
    }

    private onPageLoadSuccess() {
        this.stateSource$.next(this.createCurrentState());
    }

    private onPageLoadError(exceptionResponse: any) {
        this.stateSource$.next({
            ...this.createCurrentState(),
            loadPageErrorMessage: exceptionResponse?.text ?? exceptionResponse?.message ?? JSON.stringify(exceptionResponse)
        });
    }
}

export function createSearchManagerBackedPageableList<ITEM extends Identified<ID>, ID, PARAMS extends SearchParams>(searchManager: SearchManager<ITEM, PARAMS>) {
    const loadListItems: LoadListItemsFunction<ITEM, ID, EmptyObject> = async (pageable) => {
        searchManager.setFrontendParams({...searchManager.getFrontendParams(), pageSize: pageable.pageSize});
        searchManager.requestPage({pageNumber: pageable.pageNumber});
        const searchResult = await firstValueFrom(searchManager.getSearchResult$());
        return searchResult.content;
    }

    const loadListSize: LoadListSizeFunction<EmptyObject> = async () => {
        if (isNullOrUndefined(searchManager.lastResult()?.totalElements)) {
            searchManager.newSearch();
            await firstValueFrom(searchManager.getSearchResult$());
        }
        return {value: searchManager.lastResult().totalElements};
    }

    return new DefaultReactivePageableList({
        loadListItems,
        loadListSize,
        pageSize: searchManager.getCompleteParams().pageSize,
    });
}
