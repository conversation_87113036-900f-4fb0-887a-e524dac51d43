import {type State, states} from 'shared/constants/portaro.constants';
import {byProperty, removeAll} from 'shared/utils/array-utils';
import {Subject, BehaviorSubject, mergeWith, type Observable, firstValueFrom} from 'rxjs';
import {distinctUntilChanged, map} from 'rxjs/operators';
import {diffDictionaryObjects, exists, isNullOrUndefined} from 'shared/utils/custom-utils';
import type {SearchManager, SearchManagerState} from './search-manager';
import type {PagedParameteredResult, SearchParams} from 'typings/portaro.be.types';
import {isEqual} from 'lodash-es';
import {shareWithRefCountAndReplayLast} from 'shared/utils/observables-utils';
import type {Logger} from 'core/logging/types';

export interface Page<T = any> {
    pageNumber: number;
    first: boolean;
    last: boolean;
    items: T[];
    lastItemPosition: string;
}

interface PaginationState {
    page: State;
    previousPage: State;
    nextPage: State;
    distantPage: State;
}

export interface PageRequest {
    pageNumber: number;
    afterPosition?: string;
    beforePosition?: string;
}

export interface PaginationData<ITEM> {
    pages: Page<ITEM>[];
    showedPageNumbers: number[];
    items: ITEM[];
    state: PaginationState;
    pageSize: number;
    totalPages?: number | undefined;
    previousPageNumber: number;
    nextPageNumber: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    lowestPage: Page<ITEM> | null;
    highestPage: Page<ITEM> | null;
    loadPageErrorMessage: string | null;
    afterPosition?: string;
    beforePosition?: string;
}

export class Pagination<ITEM> {

    private pages: Page<ITEM>[] = [];
    private showedPageNumbers: number[] = [];
    private items: ITEM[] = [];
    private state: PaginationState = {
        page: states.NORMAL,
        previousPage: states.NORMAL,
        nextPage: states.NORMAL,
        distantPage: states.NORMAL
    };
    private pageSize = -1;
    private totalPages = -1;
    private previousPageNumber = -1;
    private nextPageNumber = -1;
    private afterPosition: string | null = null;
    private beforePosition: string | null = null;
    private loadPageErrorMessage: string | null = null;
    private lastFrontendParams: Partial<SearchParams> = null;
    private lastFrontendParamsDelta: Partial<SearchParams> = null;

    private readonly paginationData$: Observable<PaginationData<ITEM>>;
    private readonly manualData$: Subject<PaginationData<ITEM>> = new BehaviorSubject(this.getPaginationDataObject());
    private readonly pageLoadedNotifier = new Subject<void>();

    constructor(private readonly searchManager: SearchManager<ITEM>, private logger: Logger) {
        this.paginationData$ = this.manualData$.pipe(
            distinctUntilChanged(isEqual),
            mergeWith(
                this.searchManager.getState$().pipe(map((state) => this.reactToState(state)), distinctUntilChanged(isEqual)),
                this.searchManager.getSearchResult$().pipe(map((result) => this.reactToResult(result)))
            ),
            shareWithRefCountAndReplayLast()
        );
    }

    public getPaginationDataObject(): PaginationData<ITEM> {
        return {
            pages: [...this.pages],
            showedPageNumbers: [...this.showedPageNumbers],
            items: [...this.items],
            state: {...this.state},
            pageSize: this.pageSize,
            totalPages: this.totalPages,
            previousPageNumber: this.previousPageNumber,
            nextPageNumber: this.nextPageNumber,
            afterPosition: this.afterPosition,
            beforePosition: this.beforePosition,
            loadPageErrorMessage: this.loadPageErrorMessage,
            lowestPage: exists(this.getLowestPage()) ? {...this.getLowestPage()} : null,
            highestPage: exists(this.getHighestPage()) ? {...this.getHighestPage()} : null,
            hasNextPage: this.hasNext(),
            hasPreviousPage: this.hasPrevious()
        };
    }

    public requestPage(request: PageRequest): void {
        this.logger.info('Pagination - requested page:', request)
        this.searchManager.requestPage(request);
    }

    public requestNextPage(): void {
        if (this.hasNext()) {
            return this.requestPage({
                pageNumber: this.nextPageNumber,
                afterPosition: this.afterPosition
            });
        }
        throw new Error('Not any next page');
    }

    public requestPreviousPage(): void {
        if (this.hasPrevious()) {
            return this.requestPage({
                pageNumber: this.previousPageNumber,
                beforePosition: this.beforePosition
            });
        }
        throw new Error('Not any previous page');
    }

    public reloadPage(pageNumber: number): void {
        const isPageLoaded = exists(this.pages.find((page) => page.pageNumber === pageNumber));
        if (!isPageLoaded) {
            throw new Error(`Page ${pageNumber} is not loaded.`)
        }
        this.searchManager.requestPageReload(pageNumber);
    }

    public reloadLastPage(): void {
        if (this.pages.length === 0) {
            throw new Error('No page is loaded');
        }
        const lastPage = this.pages.at(-1);
        this.pages = [lastPage];
        this.reloadPage(lastPage.pageNumber);
    }

    public reset(pageSize?: number): void {
        this.clear();
        this.totalPages = -1;
        if (exists(pageSize)) {
            this.setPageSize(pageSize);
        }
        this.recomputePageNumbers();

        this.manualData$.next(this.getPaginationDataObject());
    }

    public replaceItem(updatedItem: ITEM, predicate: (existingItem: ITEM) => boolean): void {
        const oldItem = this.items.find(predicate);
        const oldItemIndex = this.items.indexOf(oldItem);

        if (oldItemIndex !== -1) {
            this.items[oldItemIndex] = updatedItem;
            this.manualData$.next(this.getPaginationDataObject());
        }
    }

    public async requestAllNextPages() {
        while (this.hasNext()) {
            this.requestNextPage();
            await firstValueFrom(this.pageLoadedNotifier);
        }
    }

    public async requestAllPreviousPages() {
        while (this.hasPrevious()) {
            this.requestPreviousPage();
            await firstValueFrom(this.pageLoadedNotifier);
        }
    }

    public getPaginationData$(): Observable<PaginationData<ITEM>> {
        return this.paginationData$;
    }

    private reactToState(state: SearchManagerState): PaginationData<ITEM> {
        this.loadPageErrorMessage = state.loadPageErrorMessage;
        if (!isEqual(this.lastFrontendParams, state.frontendParams)) {
            this.lastFrontendParamsDelta = diffDictionaryObjects(this.lastFrontendParams, state.frontendParams);
        }
        this.lastFrontendParams = state.frontendParams;
        if (exists(state.pageLoadInProgress)) {
            this.setProcessingStates(state.pageLoadInProgress.pageNumber);
            if (!this.isContiguous(state.pageLoadInProgress.pageNumber) && !this.isPageReload(state.pageLoadInProgress.pageNumber)) {
                // pokud neni tato stranka primo navazujici (netvori souvislou radu se soucasnymi), ostatni smazeme
                this.clear();
            }
        } else {
            this.resetProcessingStates();
        }
        return this.getPaginationDataObject();
    }

    private reactToResult(result: PagedParameteredResult<ITEM, any>): PaginationData<ITEM> {
        this.setPageSize(result.pageSize);
        this.setTotalElements(result.totalElements);
        const page = this.makePage(result);
        this.addPage(page);
        this.pageLoadedNotifier.next();
        this.logger.info('Pagination - new result: ', result)
        return this.getPaginationDataObject();
    }

    private makePage(result: PagedParameteredResult<ITEM, any>): Page<ITEM> {
        return {
            first: result.first,
            last: result.last,
            items: result.content,
            pageNumber: result.pageNumber,
            lastItemPosition: result.lastItemPosition
        }
    }

    private setTotalElements(totalElements: number | undefined): void {
        if (this.pageSize && exists(totalElements)) {
            this.totalPages = Math.ceil(totalElements / this.pageSize);
        }
    }

    private setPageSize(pageSize: number): void {
        this.pageSize = pageSize;
    }

    private getLowestPage(): Page<ITEM> {
        return this.pages.length === 0 ? null : this.pages[0];
    }

    private getHighestPage(): Page<ITEM> {
        return this.pages.length === 0 ? null : this.pages[this.pages.length - 1];
    }

    private hasPrevious(): boolean {
        return this.previousPageNumber !== -1;
    }

    private hasNext(): boolean {
        return this.nextPageNumber !== -1;
    }

    private clear(): void {
        this.pages = [];
        this.items = [];
        this.previousPageNumber = -1;
        this.nextPageNumber = -1;
        this.afterPosition = null;
        this.beforePosition = null;

    }

    private setProcessingStates(pageNumber: number): void {
        this.resetProcessingStates()
        this.state.page = states.PROCESSING;
        if (pageNumber === this.previousPageNumber) {
            this.state.previousPage = states.PROCESSING;
        } else if (pageNumber === this.nextPageNumber) {
            this.state.nextPage = states.PROCESSING;
        } else if (isNullOrUndefined(pageNumber) || !this.isPageReload(pageNumber)) {
            this.state.distantPage = states.PROCESSING;
        }
    }

    private resetProcessingStates(): void {
        this.state.page = states.NORMAL;
        this.state.previousPage = states.NORMAL;
        this.state.nextPage = states.NORMAL;
        this.state.distantPage = states.NORMAL;
    }

    /**
     * Vrati, zda je dana stranka navazujici na aktualne nactene nebo jiz nactena
     */
    private isContiguous(pageNumber: number): boolean {
        if (this.pages.length === 0) {
            return true;
        }
        const isPreviousPage = pageNumber === this.previousPageNumber;
        const isNextPage = pageNumber === this.nextPageNumber;

        return isPreviousPage || isNextPage;
    }

    private isPageReload(pageNumber: number): boolean {
        const isAlreadyLoadedPage = this.pages.length > 0 && pageNumber >= this.getLowestPage().pageNumber && pageNumber <= this.getHighestPage().pageNumber;
        if (!isAlreadyLoadedPage) {
            return false;
        }

        if (isNullOrUndefined(this.lastFrontendParamsDelta)) {
            return false;
        }

        const diffKeys = Object.keys(this.lastFrontendParamsDelta);

        const currentPageReload = diffKeys.length === 1 && diffKeys.includes('searchDate');
        const otherPageReload = diffKeys.length === 2 && diffKeys.includes('searchDate') && diffKeys.includes('pageNumber') && this.lastFrontendParamsDelta.pageNumber === pageNumber;
        return currentPageReload || otherPageReload;
    }


    private addPage(page: Page): void {
        // kontrola na nacteni stranky, ktera nenavazuje na aktualne nactene
        if (this.isContiguous(page.pageNumber) || this.isPageReload(page.pageNumber)) {
            this.pages = removeAll(this.pages, byProperty('pageNumber', page.pageNumber));
        } else {
            this.clear();
        }

        this.pages.push(page);
        this.sortPages();

        this.previousPageNumber = this.pages[0].first ? -1 : this.pages[0].pageNumber - 1;
        this.nextPageNumber = this.pages[this.pages.length - 1].last ? -1 : this.pages[this.pages.length - 1].pageNumber + 1;
        this.afterPosition = page.lastItemPosition;
        this.recomputePageNumbers();

        this.items = [];

        this.items = this.pages.flatMap((currentPage) => currentPage.items);
    }

    private sortPages(): void {
        this.pages.sort((a, b) => a.pageNumber - b.pageNumber);
    }

    private recomputePageNumbers(): void {
        this.showedPageNumbers = [];

        if (this.totalPages <= 1) {
            return;
        }

        const currentPageNumber = this.pages[this.pages.length - 1].pageNumber;
        const numberOfShowedPages = 10;
        const firstShowedPage = currentPageNumber - (numberOfShowedPages / 2 - 2) < 1 ? 1 : currentPageNumber - (numberOfShowedPages / 2 - 2);
        const lastShowedPage = firstShowedPage + numberOfShowedPages - 1 > this.totalPages ? this.totalPages : firstShowedPage + numberOfShowedPages - 1;

        for (let i = firstShowedPage; i <= lastShowedPage; i++) {
            this.showedPageNumbers.push(i);
        }
    }
}