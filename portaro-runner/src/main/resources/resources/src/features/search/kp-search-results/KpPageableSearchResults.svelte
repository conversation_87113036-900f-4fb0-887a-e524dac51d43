<script lang="ts" generics="ITEM">
    import type {Pagination, PaginationData} from '../search-manager/pagination';
    import type {ComponentSize} from 'shared/ui-widgets/types';
    import {strParams} from 'shared/utils/pipes';
    import {onDestroy} from 'svelte';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {exists} from 'shared/utils/custom-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {states} from 'shared/constants/portaro.constants';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpLoadingInline from 'shared/components/kp-loading/KpLoadingInline.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpPaginationButtons from './KpPaginationButtons.svelte';
    import KpSearchResultsContent from './KpSearchResultsContent.svelte';

    export let pagination: Pagination<ITEM>;
    export let loadingSize: ComponentSize = 'md';
    export let showPageButtons = true;
    export let showEmptyResultsMessage = true;
    export let showLoadAllButtons = false;
    export let loadNextButtonAdditionalClasses = '';
    export let loadPreviousButtonAdditionalClasses = '';

    const localize = getLocalization();

    let paginationData: PaginationData<ITEM> | undefined;
    const paginationDataSubscription = pagination.getPaginationData$().subscribe((data) => paginationData = data);

    onDestroy(() => {
        unsubscribeAllSubscriptions(paginationDataSubscription);
    });
</script>

{#if exists(paginationData)}
    <div class="kp-pageable-search-results">
        {#if paginationData.hasPreviousPage}
            <div class="pagination-buttons">
                <KpButton additionalClasses="{loadPreviousButtonAdditionalClasses}"
                          isDisabled="{paginationData.state.previousPage === states.PROCESSING}"
                          on:click={() => pagination.requestPreviousPage()}>

                    {#if paginationData.state.previousPage === states.PROCESSING}
                        <IconedContent>
                            <KpLoadingInline size="xs"/>
                            {localize(/* @kp-localization commons.LoadingInProgress */ 'commons.LoadingInProgress')}
                        </IconedContent>
                    {:else}
                        <IconedContent icon="search">
                            {pipe(localize(/* @kp-localization vysledky.NacistPredchozichX */ 'vysledky.NacistPredchozichX'), strParams(paginationData.pageSize.toString()))}
                        </IconedContent>
                    {/if}
                </KpButton>

                {#if showLoadAllButtons}
                    <KpButton additionalClasses="{loadPreviousButtonAdditionalClasses}"
                              isDisabled="{paginationData.state.previousPage === states.PROCESSING}"
                              on:click={() => pagination.requestAllPreviousPages()}>
                        Načti vše (předchozí)
                    </KpButton>
                {/if}
            </div>
        {/if}

        <KpSearchResultsContent {paginationData} {loadingSize} {showEmptyResultsMessage}>
            <slot {paginationData}></slot>
        </KpSearchResultsContent>

        {#if paginationData.hasNextPage}
            <div class="pagination-buttons">
                <KpButton additionalClasses="{loadNextButtonAdditionalClasses}"
                          isDisabled="{paginationData.state.nextPage === states.PROCESSING}"
                          on:click={() => pagination.requestNextPage()}>

                    {#if paginationData.state.nextPage === states.PROCESSING}
                        <IconedContent>
                            <KpLoadingInline size="xs"/>
                            {localize(/* @kp-localization commons.LoadingInProgress */ 'commons.LoadingInProgress')}
                        </IconedContent>
                    {:else}
                        <IconedContent icon="search">
                            {#if !paginationData.pageSize}
                                {localize(/* @kp-localization vysledky.NacistDalsichX */ 'vysledky.NacistDalsi')}
                            {:else}
                                {pipe(localize(/* @kp-localization vysledky.NacistDalsichX */ 'vysledky.NacistDalsichX'), strParams(paginationData.pageSize.toString()))}
                            {/if}
                        </IconedContent>
                    {/if}
                </KpButton>

                {#if showLoadAllButtons}
                    <KpButton additionalClasses="{loadNextButtonAdditionalClasses}"
                              isDisabled="{paginationData.state.nextPage === states.PROCESSING}"
                              on:click={() => pagination.requestAllNextPages()}>
                        Načti vše (následující)
                    </KpButton>
                {/if}
            </div>
        {/if}

        {#if showPageButtons}
            <KpPaginationButtons {paginationData} on:request-page={(event) => pagination.requestPage({pageNumber: event.detail})}/>
        {/if}
    </div>
{/if}

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .kp-pageable-search-results {
        display: flex;
        flex-direction: column;
        gap: @panel-body-padding;
    }

    .pagination-buttons {
        display: flex;
        flex-direction: row;
        justify-content: center;
        gap: 2em;
    }
</style>