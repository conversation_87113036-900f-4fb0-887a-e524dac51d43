import {SearchManager} from './search-manager';
import type {
    Form,
    SearchParams,
    StaticSearchParams,
    ViewableSearch
} from 'typings/portaro.be.types';
import {Kind, SearchType} from 'shared/constants/portaro.constants';
import {MemoryStateManager} from 'shared/state-manager/memory-state-manager';
import type {LoadFormsFunction, SearchFunction} from './search-manager-factory.service';
import {firstValueFrom, lastValueFrom, takeWhile} from 'rxjs';
import {VoidLogger} from 'core/logging/void-logger';

describe('SearchManager tests', () => {
    let searchManager: SearchManager<number>
    let staticParams: Partial<StaticSearchParams>;
    let loadSearch: SearchFunction<SearchParams, number>;
    let search: ViewableSearch<SearchParams, number>;
    let forms: Form[];
    let loadForms: LoadFormsFunction<SearchParams>;

    beforeEach(() => {
        staticParams = {type: SearchType.TYPE_MATCH_SEARCH};

        forms = [{id: 1}, {id: 2}] as Form[];

        search = {
            result : {
                params: {kind: [Kind.KIND_MATCH], type: SearchType.TYPE_MATCH_SEARCH, sorting: {id: 'name', text: 'Name', field: 'name', asc: true}},
                pageSize : 20,
                pageNumber: 1,
                totalElements :5,
                content: [1,2,3,4,5],
                first: true,
                last: true
            },
            forms: [...forms, {id:3}]
        } as ViewableSearch<SearchParams, number>;
        loadSearch = jasmine.createSpy().and.resolveTo(search);
        loadForms = jasmine.createSpy().and.resolveTo(forms);
        searchManager = new SearchManager(new VoidLogger(), new MemoryStateManager(staticParams), staticParams, loadSearch, loadForms);
    });

    describe('searching', () => {
        it('should search without static params', async () => {
            searchManager.newSearch();
            const lastSearch = await firstValueFrom(searchManager.getLastSearch$());

            expect(loadSearch).toHaveBeenCalledWith({type: SearchType.TYPE_MATCH_SEARCH, pageNumber: 1});
            expect(searchManager.getFrontendParams()).toEqual({type: SearchType.TYPE_MATCH_SEARCH, pageNumber: 1, pageSize: 20, sorting: {id: 'name', text: 'Name', field: 'name', asc: true}})
            expect(lastSearch).toEqual(search);
            expect(lastSearch.forms).toEqual(search.forms);
            expect(lastSearch.result).toEqual(search.result);
            const pagination = searchManager.getPagination();
            const paginationDataWithFilledItems = await lastValueFrom(pagination.getPaginationData$().pipe(takeWhile(({items}) => items.length === 0, true)))
            expect(paginationDataWithFilledItems.items).toEqual(search.result.content);
        });


        it('should search with new static params', async () => {
            searchManager.newSearchWithParams({type: SearchType.TYPE_PAYMENT_SEARCH});
            const lastSearch = await firstValueFrom(searchManager.getLastSearch$());

            expect(loadSearch).toHaveBeenCalledWith({type: SearchType.TYPE_PAYMENT_SEARCH, pageNumber: 1});
            expect(searchManager.getFrontendParams()).toEqual({type: SearchType.TYPE_PAYMENT_SEARCH, pageNumber: 1, pageSize: 20, sorting: {id: 'name', text: 'Name', field: 'name', asc: true}})
            expect(lastSearch).toEqual(search);
            expect(lastSearch.forms).toEqual(search.forms);
            expect(lastSearch.result).toEqual(search.result);
            const pagination = searchManager.getPagination();
            const paginationDataWithFilledItems = await lastValueFrom(pagination.getPaginationData$().pipe(takeWhile(({items}) => items.length === 0, true)))
            expect(paginationDataWithFilledItems.items).toEqual(search.result.content);
        });
    });

    describe('form loading', () => {
        it('should return empty forms', () => {
            expect(searchManager.getForms()).toEqual([]);
        });

        it('should load forms', async () => {
            await searchManager.loadForms();
            expect(searchManager.getForms()).toEqual(forms);
        });

        it('should overwrite forms with forms from search', async () => {
            await searchManager.loadForms();
            searchManager.newSearch();
            await firstValueFrom(searchManager.getLastSearch$());
            expect(searchManager.getForms()).toEqual(search.forms);
        });
    });
});