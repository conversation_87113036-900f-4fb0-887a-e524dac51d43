import type {StateManager} from './state-manager';
import type {Observable, Subject} from 'rxjs';
import {BehaviorSubject, ReplaySubject} from 'rxjs';
import {distinctUntilChanged} from 'rxjs/operators';
import {isEqual} from 'lodash-es';
import {shareWithRefCountAndReplayLast} from 'shared/utils/observables-utils';

export class MemoryStateManager<STATE extends Record<string, any>> implements StateManager<STATE> {
    private readonly stateSource$: Subject<STATE>;
    private readonly state$: Observable<STATE>;
    private currentParams: STATE;

    constructor(initialParams: STATE, pushInitialPrams = false) {
        this.currentParams = {...initialParams}
        this.stateSource$ = pushInitialPrams ? new BehaviorSubject({...this.currentParams}) : new ReplaySubject(1);
        this.state$ = this.stateSource$.pipe(distinctUntilChanged(isEqual), shareWithRefCountAndReplayLast());
    }

    public getState$(): Observable<STATE> {
        return this.state$;
    }

    public requestChangeState(newParams: STATE) {
        this.currentParams = {...this.currentParams, ...newParams};
        this.locationParamsChanged();
    }

    public requestChangeStateParameter<T extends keyof STATE>(parameterName: T, newValue: STATE[T]) {
        if (!Object.hasOwn(this.currentParams, parameterName) || this.currentParams[parameterName] !== newValue) {
            this.currentParams[parameterName] = newValue;
            this.locationParamsChanged();
        }
    }

    private locationParamsChanged() {
        this.stateSource$.next({...this.currentParams});
    }

}