<script lang="ts" generics="ROW">
    import type {Readable} from 'svelte/store';
    import type {Table} from '@tanstack/svelte-table';
    import {Render} from 'svelte-render';
    import KpGenericGridRow from 'shared/ui-widgets/grid/KpGenericGridRow.svelte';
    import KpGenericGridCell from 'shared/ui-widgets/grid/grid-cell/KpGenericGridCell.svelte';
    import {getRenderConfig} from 'shared/ui-widgets/grid/utils';
    import {getGridContext} from 'shared/ui-widgets/grid/grid.context';
    import {clickOutside} from 'shared/svelte-actions/use.click-outside';
    import {bindInputs, InputsController} from 'src/features/record-grid/lib/inputs-controller';


    export let table$: Readable<Table<ROW>>

    const {stateManager, command$, settings: {rowClassTemplate}} = getGridContext<ROW>();
    const inputsController = new InputsController(stateManager, command$, table$);
</script>
<div class="kp-generic-grid-body"
     use:clickOutside={{ignoredElementsSelectors: ['dialog']}}
     use:bindInputs={inputsController}>
    {#each $table$.getRowModel().rows as row, rowIndex (row.id)}
        <KpGenericGridRow cssClass="{rowClassTemplate(row)}">
            {#each row.getVisibleCells() as cell, columnIndex (cell.id)}
                <KpGenericGridCell {cell} {rowIndex} {columnIndex}>
                    <Render of={getRenderConfig(cell.column.columnDef.cell, cell.getContext())}/>
                </KpGenericGridCell>
            {/each}
        </KpGenericGridRow>
    {/each}
</div>

