import {stringifyArgument} from 'shared/utils/error-utils';

/**
 * @module string-utils
 * @name format
 * @kind function
 *
 * @param {*=} params Parameters
 *
 * @return {function(string)}
 *
 * @description
 * Do the same thing as [JAVA MessageFormat.format](https://docs.oracle.com/javase/7/docs/api/java/text/MessageFormat.html#format(java.lang.String,%20java.lang.Object...))
 *
 * @example
 * ```js
 *      format("world")("Hello {1}"); // "Hello world"
 * ```
 */
export function format(...params: string[]) {
    return (text: string) => {
        if (text === undefined || text === null) {
            throw new TypeError('`text` must be defined');
        }

        let str = String(text);
        let i = params.length;

        while (i--) {
            str = str.replace(new RegExp(`\\{${i}\\}`, 'gm'), params[i]);
        }

        return str;
    }
}

/**
 * @module string-utils
 * @name strToBool
 * @kind function
 *
 * @param {string} str Converted value
 *
 * @returns {boolean}
 *
 * @description
 * Converts string to boolean.
 */
export function strToBool(str: string): boolean {
    if (typeof str !== 'string') {
        throw new TypeError(`Cannot convert ${typeof str} to boolean.`);
    }

    return str.toLocaleLowerCase() === 'true';
}


export function camelCaseToKebabCase(name: string) {
    return name.replace(/[A-Z]/g, (capitalLetter) => `-${capitalLetter.toLowerCase()}`);
}

export function kebabCaseToCamelCase(name: string) {
    return name.replace(/-([a-z])/g, (_, letterAfterDash: string) => letterAfterDash.toUpperCase());
}

/**
 * @module string-utils
 * @name escapeStringForCssClassUsage
 * @kind function
 *
 * @param value Value to escape
 *
 * @returns {string}
 *
 * @description
 * Escapes string for usage as a CSS class
 */
export function escapeStringForCssClassUsage(value: string): string {
    return value.replace(/\./g, '_');
}


export function isString(value: unknown): value is string {
    return typeof value === 'string';
}

export function assertIsString(value: any): asserts value is string {
    if (!isString(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a string`);
    }
}

export function isNonEmptyString(value: any): value is string {
    return isString(value) && value.length > 0;
}

export function assertIsNonEmptyString(value: any): asserts value is string {
    if (!isNonEmptyString(value)) {
        throw new TypeError(`${stringifyArgument(value)} is not a string or is empty`);
    }
}

// https://developer.mozilla.org/en-US/docs/Web/API/Window/btoa#unicode_strings
export function utfToBase64(utfString: string): string {
    const byteArray = new TextEncoder().encode(utfString);
    const binaryString = Array.from(byteArray, (byte) => String.fromCodePoint(byte)).join('');
    return btoa(binaryString);
}

// https://developer.mozilla.org/en-US/docs/Web/API/Window/btoa#unicode_strings
export function base64ToUtf(base64: string): string {
    const binaryString = atob(base64);
    const byteArray = Uint8Array.from(binaryString, (char) => char.codePointAt(0));
    return new TextDecoder().decode(byteArray);
}

