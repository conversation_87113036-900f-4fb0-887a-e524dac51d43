package cz.kpsys.portaro.marcxml.convert;

import cz.kpsys.portaro.marcxml.model.*;
import org.hamcrest.MatcherAssert;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;

import static org.hamcrest.Matchers.equalTo;

@Tag("ci")
@Tag("unit")
class JacksonStringToRecordMarcDtoConverterTest {

    private static final String xmlMarc21Record = """
            <record xmlns="http://www.loc.gov/MARC21/slim">
              <leader>-----nam-a22-----1a-4500</leader>
              <controlfield tag="001">kpm01304368</controlfield>
              <controlfield tag="007">ta</controlfield>
              <datafield tag="022" ind1="#" ind2="#">
                <subfield code="a">80-85955-30-X (váz.)</subfield>
              </datafield>
              <datafield tag="245" ind1="1" ind2="0">
                <subfield code="a">Almanach českých šlechtických a rytířských rodů [2005] /</subfield>
                <subfield code="c">[Milan M. Buben, Karel Vavřínek ; ilustrovali Milan M. Buben a Julie Bubnová-Mühlová]</subfield>
              </datafield>
              <datafield tag="100" ind1="1" ind2="#">
                <subfield code="a" fond_id="31" record_id="e0990143-6dbc-4689-8929-1f88621f2bd3">Dačický z Heslova, Mikuláš,</subfield>
                <subfield code="d" fond_id="31" record_id="e0990143-6dbc-4689-8929-1f88621f2bd3">1555-1626</subfield>
                <subfield code="7" fond_id="31" record_id="e0990143-6dbc-4689-8929-1f88621f2bd3">jk01021980</subfield>
                <subfield code="4">aut</subfield>
              </datafield>
            </record>
            """;

    @Test
    void shouldConvertStrictFromXml() {
        JacksonStringToRecordMarcDtoConverter<StrictVerbisRecordMarcDto> converter = JacksonStringToRecordMarcDtoConverter.ofStrict();
        StrictVerbisRecordMarcDto actual = converter.convert(xmlMarc21Record);
        StrictVerbisRecordMarcDto expected = createSimpleRecord();
        MatcherAssert.assertThat(actual, equalTo(expected));
    }

    @Test
    void shouldConvertLenientFromXml() {
        JacksonStringToRecordMarcDtoConverter<LenientRecordMarcDto> converter = JacksonStringToRecordMarcDtoConverter.ofLenient();
        converter.convert(xmlMarc21Record);
    }

    private static StrictVerbisRecordMarcDto createSimpleRecord() {
        return new StrictVerbisRecordMarcDto(
                "-----nam-a22-----1a-4500",
                List.of(
                        new StrictControlfieldMarcDto("001", "kpm01304368"),
                        new StrictControlfieldMarcDto("007", "ta")
                ),
                List.of(
                        new StrictVerbisDatafieldMarcDto("022", "#", "#", List.of(
                                StrictVerbisSubfieldMarcDto.ofWithoutRecord("a", "80-85955-30-X (váz.)")
                        )),
                        new StrictVerbisDatafieldMarcDto("245", "1", "0", List.of(
                                StrictVerbisSubfieldMarcDto.ofWithoutRecord("a", "Almanach českých šlechtických a rytířských rodů [2005] /"),
                                StrictVerbisSubfieldMarcDto.ofWithoutRecord("c", "[Milan M. Buben, Karel Vavřínek ; ilustrovali Milan M. Buben a Julie Bubnová-Mühlová]")
                        )),
                        new StrictVerbisDatafieldMarcDto("100", "1", "#", List.of(
                                StrictVerbisSubfieldMarcDto.ofWithRecord("a", UUID.fromString("e0990143-6dbc-4689-8929-1f88621f2bd3"), 31, "Dačický z Heslova, Mikuláš,"),
                                StrictVerbisSubfieldMarcDto.ofWithRecord("d", UUID.fromString("e0990143-6dbc-4689-8929-1f88621f2bd3"), 31, "1555-1626"),
                                StrictVerbisSubfieldMarcDto.ofWithRecord("7", UUID.fromString("e0990143-6dbc-4689-8929-1f88621f2bd3"), 31, "jk01021980"),
                                StrictVerbisSubfieldMarcDto.ofWithoutRecord("4", "aut")
                        ))
                )
        );
    }

}