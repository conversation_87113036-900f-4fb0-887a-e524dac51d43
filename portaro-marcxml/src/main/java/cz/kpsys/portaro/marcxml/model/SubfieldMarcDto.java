package cz.kpsys.portaro.marcxml.model;

import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Objects;

public interface SubfieldMarcDto {

    @NonNull
    String code();

    @Nullable
    String value();

    default StrictVerbisSubfieldMarcDto toStrictVerbis() {
        if (this instanceof StrictVerbisSubfieldMarcDto strict) {
            return strict;
        }
        String nonNullValue = Objects.requireNonNull(value(), () -> "This subfield %s has not any value, cannot convert to strict subfield".formatted(code()));
        if (this instanceof VerbisSubfieldMarcDto verbisSubfieldMarcDto) {
            return StrictVerbisSubfieldMarcDto.ofWithOptionalRecord(code(), verbisSubfieldMarcDto.recordId(), verbisSubfieldMarcDto.fondId(), nonNullValue);
        }
        return StrictVerbisSubfieldMarcDto.ofWithoutRecord(code(), nonNullValue);
    }

}
