package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.UUID;

public record StrictVerbisSubfieldMarcDto(

        @JacksonXmlProperty(localName = "code", isAttribute = true)
        @NonNull
        String code,

        @JacksonXmlProperty(localName = "record_id", isAttribute = true)
        @Nullable
        UUID recordId,

        @JacksonXmlProperty(localName = "fond_id", isAttribute = true)
        @Nullable
        Integer fondId,

        @JacksonXmlText
        @NonNull
        String value

) implements VerbisSubfieldMarcDto {

    public static StrictVerbisSubfieldMarcDto ofWithoutRecord(@NonNull String code, @NonNull String value) {
        return new StrictVerbisSubfieldMarcDto(code, null, null, value);
    }

    public static StrictVerbisSubfieldMarcDto ofWithRecord(@NonNull String code, @NonNull UUID recordId, @NonNull Integer fondId, @NonNull String value) {
        return new StrictVerbisSubfieldMarcDto(code, recordId, fondId, value);
    }

    public static StrictVerbisSubfieldMarcDto ofWithOptionalRecord(@NonNull String code, @Nullable UUID recordId, @Nullable Integer fondId, @NonNull String value) {
        if (recordId != null && fondId == null) {
            throw new IllegalArgumentException("When record id is present, fond id must be present as well");
        }
        return new StrictVerbisSubfieldMarcDto(code, recordId, fondId, value);
    }
}
