package cz.kpsys.portaro.marcxml.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlText;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.UUID;

@JsonIgnoreProperties(value = "id")
public record LenientVerbisSubfieldMarcDto(

        @JacksonXmlProperty(localName = "code", isAttribute = true)
        @NonNull
        String code,

        @JacksonXmlProperty(localName = "record_id", isAttribute = true)
        @Nullable
        UUID recordId,

        @JacksonXmlProperty(localName = "fond_id", isAttribute = true)
        @Nullable
        Integer fondId,

        @JacksonXmlText
        @Nullable
        String value

) implements VerbisSubfieldMarcDto {}
