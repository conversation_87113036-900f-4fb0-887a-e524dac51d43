package cz.kpsys.portaro.messages.thread;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.user.User;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.List;

public record NewThreadCommand(

        @NonNull
        Department ctx,

        @NonNull
        UserAuthentication currentAuth,

        @Nullable
        String name,

        @Nullable
        Record linkedRecord,

        @NonNull
        List<User> participants
) {
}
