package cz.kpsys.portaro.record;

import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.object.*;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.IdAndIdsLoadable;
import cz.kpsys.portaro.config.CodebookLoaderBuilderFactory;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.department.HierarchyFinderDepartmentedFactory;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.IndicatorType;
import cz.kpsys.portaro.record.detail.dflt.*;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.load.*;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;

@Configuration
@Import({
        RecordFieldConfig.class
})
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordLoadCommonConfig {

    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Codebook<Fond, Integer> fondLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull Codebook<IndicatorType, FieldTypeId> indicatorTypeLoader;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull TransactionTemplateFactory readonlyTransactionTemplateFactory;
    @NonNull CustomSettingLoader customSettingLoader;
    @NonNull Provider<@NonNull ZoneId> databaseColumnsTimeZoneProvider;
    @NonNull Map<FieldTypeId, Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends Identified<?>>>> bindingLoaders;


    @Bean
    public IdAndIdsLoadable<RecordIdFondPair, UUID> recordFondPairsLoader() {
        return new SpringDbRecordFondPairsByIdsLoader(
                notAutoCommittingJdbcTemplate,
                queryFactory,
                fondLoader,
                readonlyTransactionTemplateFactory.get()
        );
    }

    @Bean
    public StringToInstantConverter recordFieldStringToInstantConverter() {
        return StringToInstantConverter.withoutIsoFallback(databaseColumnsTimeZoneProvider, CatalogConstants.FIELD_DATA_DATE_FORMATTER);
    }

    @Bean
    public RecordFieldsLoader legacyRecordFieldsLoader() {
        // Loading from KAT1_1
        LegacyRecordFieldEntityLoader legacyRecordFieldEntityLoader = new LegacyRecordFieldEntityLoader(notAutoCommittingJdbcTemplate, queryFactory);
        ResultConvertingRecordFieldLoader<LegacyRecordFieldEntity, LegacyRecordTopfield<?>> legacyRecordFieldLoader = new ResultConvertingRecordFieldLoader<>(legacyRecordFieldEntityLoader, new EntitiesToLegacyRecordFieldsConverter());
        ResultConvertingRecordFieldLoader<LegacyRecordTopfield<?>, Field<?>> commonPrefabRecordFieldLoader = new ResultConvertingRecordFieldLoader<>(legacyRecordFieldLoader, new LegacyRecordFieldsToFieldsConverter(recordFondPairsLoader(), fieldTypesByFondLoader, recordFieldStringToInstantConverter()));
        return decorateRecordFieldLoader(commonPrefabRecordFieldLoader);
    }

    @Bean
    public RecordFieldsLoader newRecordFieldsLoader() {
        // Loading from RECORD_FIELD
        ResultConvertingRecordFieldLoader<RecordFieldEntity, RecordField> recordFieldLoader = new ResultConvertingRecordFieldLoader<>(
                new NewRecordFieldEntityLoader(notAutoCommittingJdbcTemplate, queryFactory),
                new EntitiesToRecordFieldsConverter(recordFondPairsLoader(), fieldTypesByFondLoader),
                false
        );
        ResultConvertingRecordFieldLoader<RecordField, Field<?>> newRecordFieldLoader = new ResultConvertingRecordFieldLoader<>(
                recordFieldLoader,
                new RecordFieldsToFieldsConverter()
        );
        return decorateRecordFieldLoader(newRecordFieldLoader);
    }

    private RecordFieldsLoader decorateRecordFieldLoader(@NonNull RecordFieldLoader<Field<?>> recordFieldLoader) {
        StaticBoundRecordFieldLoader staticBoundRecordFieldLoader = new StaticBoundRecordFieldLoader(
                recordFieldLoader,
                fieldTypesByFondLoader,
                bindingLoaders
        );

        RecursiveRecordFieldsLoader recursiveLoader = new RecursiveRecordFieldsLoader(
                staticBoundRecordFieldLoader,
                fieldTypesByFondLoader
        );

        return new TransactionalRecordFieldsLoader(recursiveLoader, readonlyTransactionTemplateFactory.get());
    }

    @Bean
    public Codebook<IdentifiedValue<DefaultFieldValueId, @NotBlank String>, DefaultFieldValueId> defaultFieldValueLoader() {
        AllValuesProvider<IdentifiedValue<DefaultFieldValueId, @NotBlank String>> allValuesProvider = ConvertingAllValuesProvider.byItemConverter(
                new CustomSettingLoadingAllDefaultFieldValuesLoader(customSettingLoader),
                new CustomSettingToDefaultFieldValueConverter(departmentLoader, fondLoader)
        );
        return codebookLoaderBuilderFactory.create()
                .providedBy(allValuesProvider)
                .springStaticCached(CustomSetting.class.getSimpleName(), RecordConstants.CacheNames.DEFAULT_FIELD_VALUES)
                .build();
    }

    @Bean
    public DefaultFieldValueResolver defaultFieldValueResolver() {
        CustomSettingDefaultFieldValueResolver customSettingResolver = new CustomSettingDefaultFieldValueResolver(
                defaultFieldValueLoader(),
                HierarchyFinderDepartmentedFactory.ofDepartment(departmentLoader)
        );
        IndicatorDefaultFieldValueResolver indicatorResolver = new IndicatorDefaultFieldValueResolver(
                indicatorTypeLoader
        );
        return new CompositeDefaultFieldValueResolver(List.of(
                customSettingResolver,
                indicatorResolver
        ));
    }

}
