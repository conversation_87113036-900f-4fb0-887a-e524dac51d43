package cz.kpsys.portaro.view.web.rest.search;

import cz.kpsys.portaro.action.ActionSaver;
import cz.kpsys.portaro.action.SearchAction;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.Range;
import cz.kpsys.portaro.commons.object.repo.AllProvidingRepository;
import cz.kpsys.portaro.commons.object.repo.Repository;
import cz.kpsys.portaro.commons.object.repo.SingleValueRepository;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.exemplar.ExemplarSecurityActions;
import cz.kpsys.portaro.file.security.FileSecurityActions;
import cz.kpsys.portaro.loan.LoanSecurityActions;
import cz.kpsys.portaro.loan.ill.IllLoanSecurityActions;
import cz.kpsys.portaro.loan.ill.SeekingConstants;
import cz.kpsys.portaro.payment.PaymentSecurityActions;
import cz.kpsys.portaro.record.RecordSecurityActions;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.keywords.SearchedKeywordsSaver;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.search.view.SearchViewFactory;
import cz.kpsys.portaro.search.view.ViewableSearchDto;
import cz.kpsys.portaro.sorting.SortingItem;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.view.web.ratelimit.RateLimited;
import cz.kpsys.portaro.web.GenericApiController;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

import static cz.kpsys.portaro.app.CatalogConstants.Search.AUTHORITY_SEARCH_DEFAULT_PAGE_SIZE;
import static cz.kpsys.portaro.search.CoreSearchParams.DEPARTMENT;

@RequestMapping("/api/search")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SearchApiController extends GenericApiController {

    public static final String RECORD_PAGE_SIZE_COOKIE_NAME = "cz.kpsys.portaro.search.PageSize";
    public static final String AUTHORITY_PAGE_SIZE_COOKIE_NAME = "cz.kpsys.portaro.search.authority.PageSize";
    public static final int MAX_PAGE_SIZE = 100;

    @NonNull ActionSaver actionSaver;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull Provider<@NonNull String> publicContextPath;
    @NonNull Repository<Search<? extends MapBackedParams, ?, Paging>, UUID> searchRepository;
    @NonNull Provider<@NonNull Integer> defaultPageSizeProvider;
    @NonNull SearchViewFactory searchViewFactory;
    @NonNull AllProvidingRepository<Search<MapBackedParams, ?, Paging>, UUID> searchHistory;
    @NonNull SearchedKeywordsSaver searchedKeywordsSaver;
    @NonNull SingleValueRepository<Paging, String> pagingParamsRepository;

    @RequestMapping
    @RateLimited("search")
    public ViewableSearchDto<?, ?> search(MapBackedParams customParams,
                                          @RequestParam(value = SearchViewConstants.PAGE_NUMBER_PARAM, defaultValue = "1", required = false) Integer pageNumber,
                                          @RequestParam(value = SearchViewConstants.AFTER_POSITION, required = false) String afterPosition,
                                          @RequestParam(value = SearchViewConstants.BEFORE_POSITION, required = false) String beforePosition,
                                          @RequestParam(value = SearchViewConstants.PAGE_SIZE_PARAM, required = false) @Nullable Integer pageSize,
                                          @RequestParam(value = SearchViewConstants.EXPORTS_PARAM, defaultValue = "") List<String> exports,
                                          @RequestParam(value = SearchViewConstants.CACHE, defaultValue = "none") CacheMode cache,
                                          @RequestParam(value = SearchViewConstants.SORTING, required = false) SortingItem sorting,
                                          @CookieValue(value = RECORD_PAGE_SIZE_COOKIE_NAME, required = false) @Nullable Integer cookieRecordPageSize,
                                          @CookieValue(value = AUTHORITY_PAGE_SIZE_COOKIE_NAME, required = false) @Nullable Integer cookieAuthorityPageSize,
                                          @CurrentDepartment Department ctx,
                                          UserAuthentication currentAuth,
                                          Locale locale,
                                          HttpSession session,
                                          HttpServletResponse response) {
        //set defaults
        if (!customParams.has(CoreSearchParams.TYPE)) {
            customParams.set(CoreSearchParams.TYPE, "");
        }

        if (!customParams.has(CoreSearchParams.KIND)) {
            customParams.set(CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_RECORD));
        }

        SearchFactory<Paging> searchFactory = searchFactoryResolver.resolve(customParams);

        AbstractStandardSearch<MapBackedParams, ?, Paging> search = searchWithPaging(customParams,
                sorting,
                pageNumber,
                afterPosition,
                beforePosition,
                pageSize,
                cache,
                cookieRecordPageSize,
                cookieAuthorityPageSize,
                ctx,
                currentAuth,
                response,
                searchFactory);

        String type = customParams.get(CoreSearchParams.TYPE);

        if (isFirstPage(pageNumber, afterPosition, beforePosition) && (ObjectUtil.nullSafeEquals(type, SearchViewConstants.TYPE_GLOBAL) || ObjectUtil.nullSafeEquals(type, SearchViewConstants.TYPE_FORM))) {
            searchHistory.save(search);
        }
        searchRepository.save(search);

        //zalogovani hledanych klicovych slov pro naseptavac
        if (type.equals(SearchViewConstants.TYPE_GLOBAL) && customParams.has(CoreSearchParams.Q)) {
            searchedKeywordsSaver.save(customParams.get(CoreSearchParams.Q));
        }

        //security checks
        //TODO: refactor to map and move to some better place
        List<String> kind = customParams.get(CoreSearchParams.KIND);
        if (kind.contains(BasicMapSearchParams.KIND_RECORD)) {
            securityManager.throwIfCannot(RecordSecurityActions.RECORDS_SHOW, currentAuth, ctx, null);
        }
        if (kind.contains(BasicMapSearchParams.KIND_EXEMPLAR)) {
            securityManager.throwIfCannot(ExemplarSecurityActions.EXEMPLARS_PAGE_SHOW, currentAuth, ctx, null);
        }
        if (kind.contains(BasicMapSearchParams.KIND_USER)) {
            // TODO: tohle uz je zbytecne, protoze je vzdy omezujeme v cz.kpsys.portaro.search.AbstractStandardSearch.withIntersectingConstraintParam
            List<Department> searchedDepartments = ObjectUtil.castedGetOrNull(search.getLastCompleteParams(), MapBackedParams.class, params -> params.get(DEPARTMENT));
            Assert.notNull(searchedDepartments, "Cannot search users without specifying departments");
            searchedDepartments.forEach(searchedDepartment -> securityManager.throwIfCannot(SecurityActions.USERS_SHOW_OF_DEPARTMENT, currentAuth, ctx, searchedDepartment));
        }
        if (kind.contains(BasicMapSearchParams.KIND_PAYMENT)) {
            securityManager.throwIfCannot(PaymentSecurityActions.PAYMENTS_SHOW_ALL, currentAuth, ctx, null);
        }
        if (kind.contains(BasicMapSearchParams.KIND_FILE)) {
            securityManager.throwIfCannot(FileSecurityActions.FILES_SHOW, currentAuth, ctx, null);
        }
        if (kind.contains(BasicMapSearchParams.KIND_LOAN)) {
            securityManager.throwIfCannot(LoanSecurityActions.LOAN_SEARCH, currentAuth, ctx, null);
        }
        if (kind.contains(SeekingConstants.KIND_SEEKING)) {
            securityManager.throwIfCannot(IllLoanSecurityActions.SEEKING_SEARCH, currentAuth, ctx);
        }

        if (kind.contains(BasicMapSearchParams.KIND_MESSAGE)) {
            securityManager.throwIfCannot(SecurityActions.MESSAGE_SEARCH, currentAuth, ctx, null);
        }

        log(customParams, session);

        return searchViewFactory.create(search, customParams, type, ctx, currentAuth, locale, exports);
    }

    private static boolean isFirstPage(Integer pageNumber, String after, String before) {
        return (pageNumber != null && pageNumber == Range.FIRST_PAGE_NUMBER) && after == null && before == null;
    }

    private @NonNull AbstractStandardSearch<MapBackedParams, ?, Paging> searchWithPaging(MapBackedParams customParams, SortingItem customSorting, Integer pageNumber, String after, String before, @Nullable Integer pageSize, CacheMode cache, @Nullable Integer cookieRecordPageSize, @Nullable Integer cookieAuthorityPageSize, Department ctx, UserAuthentication currentAuth, HttpServletResponse response, SearchFactory<Paging> searchFactory) {
        AbstractStandardSearch<MapBackedParams, ?, Paging> search = searchFactory.createSearch(customParams, currentAuth, ctx);
        final int finalPageSize = getFinalPageSize(customParams, pageSize, cookieRecordPageSize, cookieAuthorityPageSize, response);

        if ((after != null || before != null) && (customParams.has(CoreSearchParams.TYPE) && customParams.get(CoreSearchParams.TYPE).equals(SearchViewConstants.TYPE_MESSAGE_SEARCH))) {
            Paging paging = resolvePaging(after, before);
            SortingItem sorting = resolveSorting(customSorting, before);
            search.fetch(paging, customSorting, customParams, ctx, cache);
        } else if (customParams.has(CoreSearchParams.TYPE) && customParams.get(CoreSearchParams.TYPE).equals(SearchViewConstants.TYPE_MESSAGE_SEARCH)) {
            search.fetch(KeysetPaging.forFirstPage(finalPageSize), customSorting, customParams, ctx, cache);
        } else {
            search.fetch(new RangePaging(pageNumber, finalPageSize), customSorting, customParams, ctx, cache);
        }

        return search;
    }

    // TODO až se začně používat i before tak se musí o testovat že to funguje.
    private SortingItem resolveSorting(@Nullable SortingItem customSorting, @Nullable String before) {
        if (before == null || customSorting == null) {
            return customSorting;
        }
        return customSorting.reverse();
    }

    private Paging resolvePaging(@Nullable String after, @Nullable String before) {
        return Optional.ofNullable(after)
                .or(() -> Optional.ofNullable(before))
                .flatMap(pagingParamsRepository::restore)
                .orElseThrow();
    }


    public void log(MapBackedParams customParams, HttpSession session) {
        if (customParams.has(CoreSearchParams.Q) && isSearchForAuthoritiesOnly(customParams)) {
            // tohle bychom mohli uz vyhodit (prestat logovat) a misto toho vyhledavani logovat nejak generictejs
            actionSaver.logAction(new SearchAction(session.getId(), SearchAction.PODTYP_AUTORITNI, customParams.get(CoreSearchParams.Q)));
        } else if (StringUtil.hasLength(customParams.get(CoreSearchParams.TYPE))) {
            switch (customParams.get(CoreSearchParams.TYPE)) {
                case SearchViewConstants.TYPE_GLOBAL -> actionSaver.logAction(new SearchAction(session.getId(), SearchAction.PODTYP_JEDNORADKOVE, customParams.get(CoreSearchParams.Q)));
                case SearchViewConstants.TYPE_FORM -> actionSaver.logAction(new SearchAction(session.getId(), SearchAction.PODTYP_SEARCH_FORM, Optional.ofNullable(customParams.get(CoreSearchParams.QT)).map(Object::toString).orElse(null)));
                case SearchViewConstants.TYPE_BY_AUTHORITY -> actionSaver.logAction(new SearchAction(session.getId(), SearchAction.PODTYP_PODLE_AUTORITY, customParams.get(CoreSearchParams.Q)));
            }
        } else if (customParams.has(CoreSearchParams.FINAL_RAW_QUERY)) {
            actionSaver.logAction(new SearchAction(session.getId(), SearchAction.PODTYP_GENERAL_BY_FINAL_LQUERY, customParams.get(CoreSearchParams.FINAL_RAW_QUERY)));
        } else if (customParams.has(CoreSearchParams.Q)) {
            actionSaver.logAction(new SearchAction(session.getId(), SearchAction.PODTYP_GENERAL_BY_KEYWORDS, customParams.get(CoreSearchParams.Q)));
        }
    }


    private int getFinalPageSize(MapBackedParams p, @Nullable Integer pageSize, @Nullable Integer cookieRecordPageSize, @Nullable Integer cookieAuthorityPageSize, HttpServletResponse response) {
        Assert.state(pageSize == null || pageSize > 0, "Search pageSize parameter must be greater than 0");
        Assert.state(pageSize == null || pageSize <= MAX_PAGE_SIZE, "Search pageSize parameter limit is %s".formatted(MAX_PAGE_SIZE));
        Assert.state(cookieRecordPageSize == null || cookieRecordPageSize > 0, "Search record pageSize cookie must be greater than 0");
        Assert.state(cookieAuthorityPageSize == null || cookieAuthorityPageSize > 0, "Search authority pageSize cookie must be greater than 0");

        String type = ObjectUtil.firstNotNull(p.get(CoreSearchParams.TYPE), "");
        final int finalPageSize;
        if (isSearchForAuthoritiesOnly(p)) { //authorities only
            if (pageSize != null) {
                finalPageSize = pageSize;
            } else if (NumberUtil.isPositive(cookieAuthorityPageSize)) {
                finalPageSize = Math.min(cookieAuthorityPageSize, MAX_PAGE_SIZE);
            } else {
                finalPageSize = AUTHORITY_SEARCH_DEFAULT_PAGE_SIZE;
            }
            if (Set.of(SearchViewConstants.TYPE_GLOBAL, SearchViewConstants.TYPE_BY_AUTHORITY, SearchViewConstants.TYPE_FIELDS, SearchViewConstants.TYPE_FORM).contains(type)) {
                savePageSizeToCookie(finalPageSize, AUTHORITY_PAGE_SIZE_COOKIE_NAME, response);
            }
        } else { //documenty nebo dokumenty+autority nebo cokoliv jineho
            if (pageSize != null) {
                finalPageSize = pageSize;
            } else if (NumberUtil.isPositive(cookieRecordPageSize)) {
                finalPageSize = Math.min(cookieRecordPageSize, MAX_PAGE_SIZE);
            } else {
                finalPageSize = defaultPageSizeProvider.get();
            }
            if (Set.of(SearchViewConstants.TYPE_GLOBAL, SearchViewConstants.TYPE_BY_AUTHORITY, SearchViewConstants.TYPE_FIELDS, SearchViewConstants.TYPE_FORM).contains(type)) {
                savePageSizeToCookie(finalPageSize, RECORD_PAGE_SIZE_COOKIE_NAME, response);
            }
        }
        return finalPageSize;
    }

    private static boolean isSearchForAuthoritiesOnly(MapBackedParams customParams) {
        return customParams.has(CoreSearchParams.SUBKIND) && customParams.get(CoreSearchParams.SUBKIND).size() == 1 && customParams.get(CoreSearchParams.SUBKIND).getFirst().equals(BasicMapSearchParams.SUBKIND_AUTHORITY);
    }

    private void savePageSizeToCookie(int pageSize, String cookieName, HttpServletResponse response) {
        Cookie cookie = new Cookie(cookieName, String.valueOf(pageSize));
        cookie.setMaxAge(7776000); //3 mesice
        cookie.setPath(publicContextPath.get().isEmpty() ? "/" : publicContextPath.get());
        response.addCookie(cookie);
    }
}
