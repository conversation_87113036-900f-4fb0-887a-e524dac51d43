package cz.kpsys.portaro.erp.workattendance;

import cz.kpsys.portaro.commons.object.IdentifiedRecord;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import lombok.NonNull;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

public record Activity(

        @NonNull
        UUID id,

        @NonNull
        UUID userId,

        @NonNull
        LocalDate startDay,

        @NonNull
        Instant startDate,

        @NonNull
        Duration regularDuration,

        @NonNull
        Duration overtimeDuration,

        @NonNull
        Duration onCallDutyDuration,

        @NonNull
        Duration absenceDuration,

        @NonNull
        Optional<AbsenceReason> absenceReason

) implements IdentifiedRecord<UUID> {

    @Override
    public boolean equals(Object o) {
        return ObjectUtil.equalsIdentified(this, o, Activity.class);
    }

    @Override
    public int hashCode() {
        return id().hashCode();
    }
}
